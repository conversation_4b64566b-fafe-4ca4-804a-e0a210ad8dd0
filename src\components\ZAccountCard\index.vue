<template>
  <!-- 充值弹窗、新增账户卡片使用样式 -->
  <div class="wrap">
    <div class="card-shadow" :style="shadowStyle"></div>
    <div class="account-card" :class="cardClasses" :style="cardStyle" @click="handleClick">
      <!-- 图标区域 -->
      <div class="icon-wrapper">
        <WithdrawTypeIcon
          :type="item.name"
          :icon="item.icon"
          :width="iconSize"
          :height="iconSize"
        />
      </div>

      <!-- 名称区域 -->
      <div class="name-wrapper">
        <AutoResizeText
          :text="item.name"
          :container-width="50"
          :container-height="36"
          :max-font-size="16"
          :min-font-size="10"
          :multi-line="true"
          :max-lines="2"
          :line-height="1.2"
          :padding="2"
        />
      </div>

      <!-- 选中状态图标 -->
      <div
        class="check-wrapper"
        :style="{
          bottom: `${checkOffset}px`,
          right: `${checkOffset}px`,
        }"
      >
        <CheckedUnCheckedIcon
          :color="cardTheme.checkBoxColor"
          :type="item.name"
          :isChecked="isSelected"
          :size="checkIconSize"
        />
      </div>

      <!-- 选中状态指示器 -->
      <div v-if="isSelected" class="selected-indicator" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { storeToRefs } from "pinia";
import { useDepositStore } from "@/stores/deposit";
import { CHANEL_TYPE } from "@/utils/config/GlobalConstant";
import CheckedUnCheckedIcon from "@/components/ZComonImg/CheckedUnCheckedIcon.vue";
import WithdrawTypeIcon from "@/components/ZComonImg/WithdrawTypeIcon.vue";
import AutoResizeText from "@/components/AutoResizeText/index.vue";
import { CARD_THEMES } from "./types";
// 类型定义
interface AccountItem {
  name: string;
  icon?: string;
  [key: string]: any;
}

interface Props {
  /** 账户项目数据 */
  item: AccountItem;
  cardWidth?: number;
  cardHeight?: number;
  checkOffset?: number;
  [key: string]: any;
}

const props = withDefaults(defineProps<Props>(), {
  cardWidth: 0,
  cardHeight: 56,
  checkOffset: 4,
});

// Store
const depositStore = useDepositStore();
const { payAccountTypeValue } = storeToRefs(depositStore);

// 计算属性
const isSelected = computed(() => payAccountTypeValue.value === props.item.account_type);

const cardTheme = computed(() => {
  return CARD_THEMES[props.item.name?.toLocaleLowerCase()] || CARD_THEMES.default;
});

const cardClasses = computed(() => ({
  "is-selected": isSelected.value,
  "is-gcash": props.item.name === CHANEL_TYPE.G_CASH,
  "is-maya": props.item.name === CHANEL_TYPE.MAYA,
  "is-clickable": !isSelected.value,
}));

const shadowStyle = computed(() => ({
  width: props.cardWidth ? `${props.cardWidth - 16}px` : "calc(100% - 16px)",
  height: `${props.cardHeight}px`,
  boxShadow: cardTheme.value.boxShadow,
}));

const cardStyle = computed(() => ({
  background: cardTheme.value.background,
  color: cardTheme.value.textColor,
  width: props.cardWidth ? `${props.cardWidth}px` : "100%",
  height: `${props.cardHeight}px`,
}));

const iconSize = computed(() => 23);
const checkIconSize = computed(() => 16);

// 方法
const handleClick = () => {
  if (isSelected.value) return;
  depositStore.setCurReChangeName(props.item.name);
};
</script>

<style lang="scss" scoped>
.wrap {
  position: relative;
  background: transparent;
  padding: 0;
  margin: 0;
  transition: all 0.2s ease;
  .card-shadow {
    position: absolute;
    border-radius: 16px;
    top: 0;
    left: 8px;
    z-index: 1;
  }

  &:hover {
    transform: translateY(-1px);
  }
}
.account-card {
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 6px 8px;
  color: #fff;
  position: relative;
  z-index: 5;
  cursor: pointer;
  box-sizing: border-box;
  overflow: hidden;

  &.is-selected {
    border-color: rgba(255, 255, 255, 0.3);
  }

  .icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
  }

  .name-wrapper {
    flex: 1;
    text-align: left;
  }

  .check-wrapper {
    position: absolute;
    /*  right: 4px;
    bottom: 4px; */
  }

  .selected-indicator {
    display: none;
  }
}
</style>
