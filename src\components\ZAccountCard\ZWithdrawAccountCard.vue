<template>
  <div class="wrap">
    <!-- 卡片底部阴影 -->
    <div class="card-shadow" :style="shadowStyle"></div>
    <!-- 卡片内容 -->
    <div class="card-wrap" :style="cardStyle" @click="handleClickCard">
      <!-- 卡片背景：账户类型logo -->
      <div class="bg-logo">
        <ZIcon :type="`icon-${cardInfo.name.toLocaleLowerCase()}`" :size="95" />
      </div>
      <div class="card-header">
        <div class="left">
          <div class="card-logo">
            <WithdrawTypeIcon :icon="cardInfo.icon" :width="35" :height="35" :padding="5" />
          </div>
          <span class="card-name">{{ cardInfo.name }}</span>
        </div>
        <div class="right">
          <template v-if="status === Status.CHECK">
            <CheckedUnCheckedIcon
              :disabled="!activeChannel"
              :type="cardInfo.name"
              :isChecked="checkedAccountId === item.account_id"
            />
          </template>
          <ZIcon v-if="status === Status.EDIT" type="icon-bianji1" color="#fff" />
        </div>
      </div>
      <div class="card-footer">
        <span class="card-num">{{ formatCardNumber(item.account_no) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { formatCardNumber } from "@/utils/core/tools";
import { getMethodsInfo, METHODS_NAMES } from "@/utils/config/GlobalConstant";
import { showToast } from "vant";
import { useWithdrawStore } from "@/stores/withdraw";
import WithdrawTypeIcon from "@/components/ZComonImg/WithdrawTypeIcon.vue";
import CheckedUnCheckedIcon from "@/components/ZComonImg/CheckedUnCheckedIcon.vue";
import ZIcon from "@/components/ZIcon/index.vue";
import { Status, CARD_THEMES, type Item } from "./types";

const emits = defineEmits(["click"]);

const props = defineProps({
  item: {
    type: Object as () => Item,
    required: true,
    default: () => ({}),
  },
  status: {
    type: String as () => Status,
    default: Status.VIEW,
    validator: (value: string) => Object.values(Status).includes(value as Status),
  },
  checkedAccountId: {
    type: String,
    default: "",
  },
  cardWidth: {
    type: Number,
    default: 0,
  },
  cardHeight: {
    type: Number,
    default: 114,
  },
});

const activeChannel = computed(() => {
  const withdrawStore = useWithdrawStore();
  const list = withdrawStore.withdrawData || [];
  const activeChannel = list.find((item: any) => item.account_type === props.item.type);
  return activeChannel;
});

const cardInfo = computed(() => {
  const base = getMethodsInfo(props.item?.type);
  console.log("base", base);
  return {
    ...props.item,
    name: base.name,
    icon: activeChannel.value?.icon,
  };
});

const cardTheme = computed(() => {
  const theme = CARD_THEMES[cardInfo.value.name.toLocaleLowerCase()];
  return theme || CARD_THEMES.default;
});

const cardStyle = computed(() => ({
  background: cardTheme.value.background,
  color: cardTheme.value.textColor,
  width: props.cardWidth ? `${props.cardWidth}px` : "100%",
  height: `${props.cardHeight}px`,
}));

const shadowStyle = computed(() => ({
  width: props.cardWidth ? `${props.cardWidth - 16}px` : "calc(100% - 16px)",
  height: `${props.cardHeight}px`,
  boxShadow: cardTheme.value.boxShadow,
}));

const handleClickCard = () => {
  if (props.status === Status.VIEW) return;
  if (!activeChannel.value) {
    showToast(
      `${METHODS_NAMES[props.item.type]} cannot withdraw, please switch to other withdrawal methods`
    );
    return;
  }
  emits("click", props.item);
};
</script>

<style scoped lang="scss">
.wrap {
  position: relative;
  background: transparent;
  padding: 0;
  margin: 0;
  transition: all 0.2s ease;
  .card-shadow {
    position: absolute;
    border-radius: 16px;
    top: 0;
    left: 8px;
    z-index: 1;
  }

  &:hover {
    transform: translateY(-1px);
  }
  .bg-logo {
    position: absolute;
    right: 8px;
    top: 0;
    bottom: 0;
    color: #fff;
    opacity: 0.1;
  }
}
.card-wrap {
  font-family: "Inter";
  width: 100%;
  border-radius: 20px;
  color: #fff;
  padding: 16px;
  box-sizing: border-box;
  // box-shadow: rgba(64, 134, 244, 0.35) 0px 10px 15px 0px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  position: relative;
  z-index: 5;
  gap: 12px;

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .left {
      display: flex;
      align-items: center;

      .card-logo {
        margin-right: 8px;
      }

      .card-name {
        color: #fff;
        font-size: 18px;
        font-weight: 700;
      }
    }

    .right {
      display: flex;
      align-items: center;
    }
  }

  .card-footer {
    display: flex;
    align-items: center;
    font-size: 20px;
    font-weight: bold;

    .card-num {
      color: #fff;
      text-align: center;
      font-family: "D-DIN";
      font-size: 28px;
      font-weight: 700;
      line-height: normal;
    }
  }
}
</style>
