# ZAccountCard 组件说明

## 概述

`ZBaseAccountCard` 是一个可配置的基础账户卡片组件，抽取了 `ZWithdrawAccountCard` 和 `index.vue` 的公共样式和功能。

## 设计特点

### 固定部分
- **背景色和阴影**：基于主题系统自动应用
- **左侧图标**：固定位置的账户类型图标
- **基础布局结构**：统一的卡片容器和阴影效果

### 可配置部分
- **右侧图标**：通过 `right` 插槽自定义
- **底部文字**：通过 `bottom` 插槽自定义
- **主要内容**：通过 `content` 插槽自定义
- **布局方式**：支持水平和垂直布局
- **背景装饰图标**：可选的大尺寸背景图标

## Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `item` | `BaseAccountItem` | - | 账户项目数据 |
| `cardWidth` | `number` | `0` | 卡片宽度，0表示100% |
| `cardHeight` | `number` | `114` | 卡片高度 |
| `leftIcon` | `string` | - | 左侧图标 |
| `leftIconSize` | `number` | `35` | 左侧图标大小 |
| `leftIconPadding` | `number` | `5` | 左侧图标内边距 |
| `showBgIcon` | `boolean` | `false` | 是否显示背景装饰图标 |
| `bgIconName` | `string` | `''` | 背景图标名称 |
| `showRightArea` | `boolean` | `false` | 是否显示右侧区域 |
| `showBottomArea` | `boolean` | `false` | 是否显示底部区域 |
| `contentLayout` | `'horizontal' \| 'vertical'` | `'vertical'` | 内容布局方式 |
| `isSelected` | `boolean` | `false` | 是否选中状态 |
| `clickable` | `boolean` | `true` | 是否可点击 |
| `extraClasses` | `string[]` | `[]` | 额外的CSS类 |

## 插槽 (Slots)

### content
主要内容区域，接收参数：
- `item`: 账户项目数据
- `theme`: 当前主题配置

### right
右侧区域，接收参数：
- `item`: 账户项目数据
- `theme`: 当前主题配置
- `isSelected`: 是否选中状态

### bottom
底部区域，接收参数：
- `item`: 账户项目数据
- `theme`: 当前主题配置

## 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `click` | `item: BaseAccountItem` | 卡片点击事件 |

## 使用示例

### 1. 提现卡片样式（垂直布局）

```vue
<ZBaseAccountCard
  :item="cardInfo"
  :card-width="300"
  :card-height="114"
  :left-icon="cardInfo.icon"
  :show-bg-icon="true"
  :bg-icon-name="cardInfo.name.toLowerCase()"
  :show-right-area="true"
  :show-bottom-area="true"
  content-layout="vertical"
  @click="handleClick"
>
  <template #content="{ item }">
    <span class="card-name">{{ item.name }}</span>
  </template>
  
  <template #right="{ item }">
    <CheckedIcon :isChecked="isSelected" />
  </template>
  
  <template #bottom="{ item }">
    <span class="card-number">{{ item.account_no }}</span>
  </template>
</ZBaseAccountCard>
```

### 2. 充值卡片样式（水平布局）

```vue
<ZBaseAccountCard
  :item="item"
  :card-height="56"
  :left-icon="item.icon"
  :left-icon-size="23"
  :show-right-area="true"
  content-layout="horizontal"
  :is-selected="isSelected"
  @click="handleClick"
>
  <template #content="{ item }">
    <AutoResizeText :text="item.name" />
  </template>
  
  <template #right="{ theme }">
    <CheckedIcon :color="theme.checkBoxColor" />
  </template>
</ZBaseAccountCard>
```

## 主题系统

组件使用 `CARD_THEMES` 配置主题，支持以下主题：
- `gcash`: GCash 蓝色主题
- `maya`: Maya 绿色主题
- `bank`: 银行橙色主题
- `palawan`: Palawan 青色主题
- `QRPH`: QRPH 红色主题
- `default`: 默认绿色主题

每个主题包含：
- `background`: 背景渐变色
- `textColor`: 文字颜色
- `boxShadow`: 阴影效果
- `checkBoxColor`: 选择框颜色

## 迁移指南

### 从 ZWithdrawAccountCard 迁移
1. 使用 `content-layout="vertical"`
2. 启用 `show-bg-icon`、`show-right-area`、`show-bottom-area`
3. 通过插槽自定义右侧操作图标和底部卡号

### 从 index.vue (充值卡片) 迁移
1. 使用 `content-layout="horizontal"`
2. 启用 `show-right-area`
3. 通过插槽自定义名称显示和右侧选择状态

## 注意事项

1. 原有的两个文件暂时保持不变，可以逐步迁移
2. 新的基础组件完全兼容现有的主题系统
3. 所有样式都通过 scoped CSS 隔离，不会影响其他组件
4. 支持响应式设计，可以适配不同屏幕尺寸
