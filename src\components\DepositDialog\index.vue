<template>
  <ZActionSheet
    v-model="show"
    title="Deposit"
    :showCancelButton="false"
    :onConfirm="depositStore.handleSubmit"
    :confirmDisabled="getConfirmDisabled"
    :closeOnClickOverlay="true"
    confirmText="Continue"
    :style="{
      maxHeight: '95vh',
    }"
  >
    <div class="content">
      <!-- 最小充值金额提示，小程序端才有 -->
      <div class="mininum-tips" v-if="firstRestriction > 0">
        <ZIcon type="icon-warn" color="#ff936f"></ZIcon>
        <span class="tips"
          >Minimum top-out of Php {{ firstRestriction }} for first-time users .</span
        >
      </div>

      <!-- 充值方式，WEB端才有，小程序端默认走自己对应的充值方式 -->
      <div class="payment-method-wrap" v-if="!isMiniChannel">
        <div class="payment-method" v-if="rechargeList.length">
          <template v-for="item in rechargeList" :key="item.id">
            <ZAccountCard :item="item" class="card-item"></ZAccountCard>
          </template>
        </div>
      </div>
      <!-- 钱包 -->
      <div class="balance-wrap">
        <img class="wallet-icon" src="@/assets/images/account/wallet.png" />Your Wallet Balance(₱)
        ：
        <span class="balance">{{ amountFormatThousands(globalStore.balance) }}</span>
      </div>
      <!-- 快速选择充值金额 -->
      <div v-if="rechargeList.length > 0">
        <!-- <div class="select-title">Select an amount</div> -->
        <div v-for="item in rechargeList" :key="item.id">
          <div v-if="item.account_type === payAccountTypeValue" class="quick-amounts-wrap">
            <FastSelect :item="item"></FastSelect>
          </div>
        </div>
      </div>
      <!-- 手动输入充值金额 -->
      <div class="amount-input" :class="{ disabled: !hasRechargeList }" @click="handleInputClick">
        <IconCoin :size="24"></IconCoin>
        <input
          type="number"
          inputmode="numeric"
          class="input"
          :value="selectedAmount"
          @input="depositStore.handleCustomAmountInput"
          :placeholder="placeholder"
          :disabled="!hasRechargeList"
        />
      </div>
      <!-- 输入校验提示 -->
      <div class="error-tips" v-if="errTip">{{ errTip }}</div>
      <div class="warn-tips" v-if="warnTip">{{ warnTip }}</div>
      <!-- 计算总额、奖励金额，小程序端才有 -->
      <div class="total-bonus" v-if="isFirstDeposit">
        <div class="row">
          <span class="row-title">Total(₱):</span>
          <span class="row-value">{{ amountFormatThousands(totalAmount) }}</span>
        </div>
        <div class="row">
          <span class="row-title">Bonus(₱):</span>
          <span class="row-value">{{ amountFormatThousands(awardNum) }}</span>
        </div>
      </div>
    </div>
  </ZActionSheet>
</template>
<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { storeToRefs } from "pinia";
import { showToast } from "vant";
import { useDepositStore } from "@/stores/deposit";
import { amountFormatThousands } from "@/utils/core/tools";
import { useGlobalStore } from "@/stores/global";
// RechargeCard 已移动到组件库，现在使用全局注册的 ZAccountCard
import FastSelect from "./FastSelect.vue";

const depositStore = useDepositStore();
const globalStore = useGlobalStore();
const {
  show,
  isMiniChannel,
  rechargeList,
  firstRestriction,
  isFirstDeposit,
  errTip,
  warnTip,
  payAccountTypeValue,
  selectedAmount,
  awardNum,
  totalAmount,
} = storeToRefs(depositStore);

const hasRechargeList = computed(() => {
  // return rechargeList.value.length > 0 && !isMiniChannel.value
  return rechargeList.value.length > 0;
});

const placeholder = computed(() => {
  if (!hasRechargeList.value) return "- -";
  return `Enter Amount ${depositStore.minAmount} - ${depositStore.maxAmount}₱`;
});

const handleInputClick = () => {
  if (!hasRechargeList.value) {
    showToast({
      message: "There is currently no deposit channel available,\nplease contact customer service",
      className: "custom-toast-width",
    });
  }
};

const getConfirmDisabled = computed(() => {
  if (!selectedAmount.value) return true;
  if (errTip.value) return true;
  if (!hasRechargeList.value) return true;
  return false;
});

watch(
  () => depositStore.show,
  (newVal) => {
    if (!newVal) {
      depositStore.resetData();
    }
  }
);
</script>

<style lang="scss" scoped>
.content {
  font-family: "Inter";
}

.row-value {
  font-family: "D-DIN";
}

.card-item {
  width: 31%;
}
.select-title {
  margin-top: 20px;
  margin-bottom: 20px;
  color: #222;
  /* 弹窗标题 */
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
}

.balance-wrap {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  color: #999;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;

  .wallet-icon {
    width: 24px;
    height: 24px;
    margin-right: 8px;
    vertical-align: middle;
  }

  .balance {
    color: #999;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;

  .checkbox-label {
    color: #999;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    line-height: normal;
  }
}

.mininum-tips {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #333;
  margin-bottom: 16px;
  background-color: #fffaf8;
  padding: 8px 12px;
  border-radius: 8px;

  img {
    width: 12px;
    height: 12px;
  }

  .tips {
    color: #ff936f;
    font-size: 12px;
  }
}

.enjoy {
  img {
    width: 100%;
    height: auto;
    margin-bottom: 16px;
  }
}

.error-tips {
  font-size: 14px;
  color: #ac1240;
  margin-top: 8px;
  font-family: D-DIN;
}
.warn-tips {
  font-size: 14px;
  color: #c0c0c0;
  margin-top: 8px;
  font-family: D-DIN;
}

.payment-method-wrap {
  display: flex;
  flex-direction: column;

  .payment-method {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 9px;
    margin-bottom: 12px;
  }

  .quick-amounts-wrap {
    // margin-top: 16px;
  }
}

.continue-btn {
  position: relative;
  margin-top: 16px;
  border-radius: 24px;
  background-color: #ac1240;
  color: #fff;
  outline: none;
  border: none;
}

.amount-input {
  display: flex;
  align-items: center;
  height: 40px;
  background: #f3f6fe;
  padding: 0 16px;
  margin-top: 16px;
  border-radius: 24px;
  transition: all 0.3s ease;

  &.disabled {
    background: #f5f5f5;
    opacity: 0.6;
    cursor: not-allowed;
  }

  .input {
    margin-left: 8px;
    flex: 1;
    height: 100%;
    border: none;
    background: transparent;
    font-size: 16px;
    color: #333;
    color: var(--Nustar, #ac1140);
    font-family: D-DIN;
    font-size: 22px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;

    &:focus {
      outline: none;
    }

    &::placeholder {
      color: #bbb;
      font-family: D-DIN;
      font-size: 16px;
      font-weight: 400;
      opacity: 0.8;
    }

    &:disabled {
      color: #ccc;
      cursor: not-allowed;
      pointer-events: none; // 禁用input的事件，让事件冒泡到父容器

      &::placeholder {
        opacity: 0.9;
      }
    }
  }
}

.total-bonus {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 16px;

  .row {
    display: flex;
    align-items: center;
    width: 100%;
    font-size: 16px;
    color: #333;
    flex: 1;

    .row-title {
      font-size: 14px;
      font-weight: 400;
      color: #999;
    }

    .row-value {
      font-weight: 700;
      color: #222;
    }

    &:last-child {
      justify-content: flex-end;

      .row-value {
        color: #ac1140;
      }
    }
  }
}
</style>

<style>
/* 全局 Toast 样式 - 不使用 scoped */
.custom-toast-width {
  max-width: 320px !important;
  min-width: 200px !important;
  white-space: pre-line;
  text-align: center;
  line-height: 1.4;
  padding: 12px 16px !important;
  border-radius: 8px !important;
}
</style>
