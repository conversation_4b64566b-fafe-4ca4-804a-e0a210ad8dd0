<template>
  <!-- 使用基础组件重构 index.vue (充值卡片) 的示例 -->
  <ZBaseAccountCard
    :item="item"
    :card-width="cardWidth"
    :card-height="cardHeight"
    :left-icon="item.icon"
    :left-icon-size="23"
    :left-icon-padding="0"
    :show-right-area="true"
    content-layout="horizontal"
    :is-selected="isSelected"
    :clickable="!isSelected"
    :extra-classes="cardExtraClasses"
    @click="handleClick"
  >
    <!-- 主要内容区域：自适应文本 -->
    <template #content="{ item }">
      <div class="name-wrapper">
        <AutoResizeText
          :text="item.name"
          :container-width="50"
          :container-height="36"
          :max-font-size="16"
          :min-font-size="10"
          :multi-line="true"
          :max-lines="2"
          :line-height="1.2"
          :padding="2"
        />
      </div>
    </template>

    <!-- 右侧区域：选中状态图标 -->
    <template #right="{ item, theme }">
      <div
        class="check-wrapper"
        :style="{
          bottom: `${checkOffset}px`,
          right: `${checkOffset}px`,
        }"
      >
        <CheckedUnCheckedIcon
          :color="theme.checkBoxColor"
          :type="item.name"
          :isChecked="isSelected"
          :size="16"
        />
      </div>
      
      <!-- 选中状态指示器 -->
      <div v-if="isSelected" class="selected-indicator" />
    </template>
  </ZBaseAccountCard>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { storeToRefs } from "pinia";
import { useDepositStore } from "@/stores/deposit";
import { CHANEL_TYPE } from "@/utils/config/GlobalConstant";
import CheckedUnCheckedIcon from "@/components/ZComonImg/CheckedUnCheckedIcon.vue";
import AutoResizeText from "@/components/AutoResizeText/index.vue";
import ZBaseAccountCard from "../ZBaseAccountCard.vue";

// 类型定义
interface AccountItem {
  name: string;
  icon?: string;
  [key: string]: any;
}

interface Props {
  /** 账户项目数据 */
  item: AccountItem;
  cardWidth?: number;
  cardHeight?: number;
  checkOffset?: number;
  [key: string]: any;
}

const props = withDefaults(defineProps<Props>(), {
  cardWidth: 0,
  cardHeight: 56,
  checkOffset: 4,
});

// Store
const depositStore = useDepositStore();
const { curRechangeMethods } = storeToRefs(depositStore);

// 计算属性
const isSelected = computed(() => curRechangeMethods.value === props.item.name);

const cardExtraClasses = computed(() => {
  const classes = [];
  
  if (props.item.name === CHANEL_TYPE.G_CASH) {
    classes.push('is-gcash');
  }
  
  if (props.item.name === CHANEL_TYPE.MAYA) {
    classes.push('is-maya');
  }
  
  return classes;
});

// 方法
const handleClick = () => {
  if (isSelected.value) return;
  depositStore.setCurReChangeName(props.item.name);
};
</script>

<style lang="scss" scoped>
.name-wrapper {
  flex: 1;
  text-align: left;
}

.check-wrapper {
  position: absolute;
}

.selected-indicator {
  display: none;
}

// 特定类型的样式可以通过 extra-classes 添加
:deep(.is-gcash) {
  // GCash 特定样式
}

:deep(.is-maya) {
  // Maya 特定样式
}
</style>
