# WithDrawDialog 组件替换总结

## 替换内容

成功将 `src/components/WithDrawDialog/index.vue` 中的 `AccountCardSelect` 组件替换为 `ZBaseAccountCard` 组件。

## 主要变更

### 1. 导入变更
```typescript
// 原来
import AccountCardSelect from "@/views/account/withdraw-account/components/AccountCardSelect.vue";

// 替换为
import ZBaseAccountCard from "@/components/ZAccountCard/ZBaseAccountCard.vue";
import { formatCardNumber } from "@/utils/core/tools";
import { getMethodsInfo } from "@/utils/config/GlobalConstant";
import ZIcon from "@/components/ZIcon/index.vue";
import { computed } from "vue";
import { storeToRefs } from "pinia";
```

### 2. 模板变更
```vue
<!-- 原来 -->
<AccountCardSelect
  @select="handleSelect"
  :item="selectedAccountInfo"
  :showSelectIcon="!isMiniChannel"
>
</AccountCardSelect>

<!-- 替换为 -->
<ZBaseAccountCard
  :item="cardInfo"
  :card-height="68"
  :left-icon="cardInfo.icon"
  :left-icon-size="35"
  :left-icon-padding="5"
  :show-right-area="!isMiniChannel"
  content-layout="horizontal"
  :clickable="!isMiniChannel"
  @click="handleSelect"
>
  <!-- 主要内容区域：显示卡片名称和卡号 -->
  <template #content="{ item }">
    <div class="card-info">
      <div class="card-name">{{ item.name }}</div>
      <div class="card-num">{{ formatCardNumber(item.account_no) }}</div>
    </div>
  </template>

  <!-- 右侧区域：显示前进箭头 -->
  <template #right>
    <ZIcon v-if="!isMiniChannel" type="icon-qianjin" :size="18" />
  </template>
</ZBaseAccountCard>
```

### 3. 逻辑变更
添加了 `cardInfo` 计算属性来处理卡片数据：

```typescript
// 计算卡片信息，类似原 AccountCardSelect 组件的逻辑
const activeChannel = computed(() => {
  const list = withdrawStore.withdrawData || [];
  const activeChannel = list.find((item: any) => item.account_type === selectedAccountInfo.value?.type);
  return activeChannel;
});

const cardInfo = computed(() => {
  if (!selectedAccountInfo.value) {
    return { name: '', icon: '', account_no: '' };
  }
  
  const base = getMethodsInfo(selectedAccountInfo.value?.type);
  return {
    ...selectedAccountInfo.value,
    name: base.name,
    icon: activeChannel.value?.icon,
  };
});
```

### 4. 样式变更
添加了匹配原 `AccountCardSelect` 组件的样式：

```scss
// 卡片信息样式，匹配原 AccountCardSelect 组件
.card-info {
  display: flex;
  flex-direction: column;
  
  .card-name {
    color: #fff;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-bottom: 2px;
  }

  .card-num {
    color: #fff;
    font-family: "D-DIN";
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
  }
}
```

## 配置参数说明

### 自动配置的参数
- `card-height="68"`：匹配原组件的高度
- `content-layout="horizontal"`：使用水平布局
- `left-icon-size="35"`：左侧图标大小
- `left-icon-padding="5"`：图标内边距
- `show-right-area="!isMiniChannel"`：根据渠道类型显示右侧区域
- `clickable="!isMiniChannel"`：根据渠道类型设置可点击性

### 动态数据绑定
- `item="cardInfo"`：绑定计算后的卡片信息
- `left-icon="cardInfo.icon"`：动态图标
- 通过插槽自定义内容显示

## 功能保持
1. ✅ 显示账户名称和卡号
2. ✅ 根据渠道类型显示/隐藏右侧箭头
3. ✅ 点击事件处理
4. ✅ 样式和布局保持一致
5. ✅ 响应式数据绑定

## 优势
1. **统一性**：使用统一的基础组件
2. **可维护性**：减少重复代码
3. **一致性**：保持与其他卡片组件的视觉一致性
4. **灵活性**：通过插槽系统提供更好的自定义能力

## 测试建议
1. 测试不同渠道类型（GCash、Maya、普通渠道）的显示
2. 测试点击事件是否正常触发
3. 测试卡号格式化是否正确
4. 测试响应式布局是否正常
