<!-- 账户列表选择下拉 -->
<template>
  <ZActionSheet
    class="z-dialog"
    v-model="showAccountListDialog"
    title="Choose payment method"
    :closeOnClickOverlay="true"
    :showCancelButton="false"
    :showConfirmButton="false"
    confirmText="Continue"
    :overlay-style="{ 'background-color': 'transparent' }"
    :style="{
      height: '100vh',
      borderRadius: 0,
      padding: '0 4px',
    }"
  >
    <div>
      <div v-for="account in accounts" :key="account.account_id">
        <ZWithdrawAccountCard
          class="card-item"
          :checkedAccountId="selectedAccountInfo?.account_id"
          :item="account"
          :status="Status.CHECK"
          @click="() => withdrawStore.handleCardCheck(account)"
          :withdrawList="withdrawData"
        >
        </ZWithdrawAccountCard>
      </div>
    </div>
    <template #extra>
      <ZButton v-if="canAddAccount" @click="handleClick"> Add Withdrawal Account </ZButton>
    </template>
  </ZActionSheet>
</template>
<script setup lang="ts">
import { useWithdrawStore } from "@/stores/withdraw";
import ZWithdrawAccountCard from "@/components/ZAccountCard/ZWithdrawAccountCard.vue";
import { Status } from "@/components/ZAccountCard/types";
import router from "@/router";
const withdrawStore = useWithdrawStore();
const { showAccountListDialog, accounts, selectedAccountInfo, withdrawData, accountLimitNum } =
  storeToRefs(withdrawStore);

const canAddAccount = computed(() => {
  return accounts.value.length < accountLimitNum.value;
});

const handleClick = () => {
  withdrawStore.showAccountListDialog = false;
  withdrawStore.showWithdrawDialog = false;
  router.push("/account/withdraw-account");
};
</script>

<style lang="scss" scoped>
.z-dialog {
  .card-item {
    margin-bottom: 20px;
  }
}

:deep(.van-action-sheet) {
  max-height: 100vh !important;
  /* 强制设置为视口高度 */
}
</style>
