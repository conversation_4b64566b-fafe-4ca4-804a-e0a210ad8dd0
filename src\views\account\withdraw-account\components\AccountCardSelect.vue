<!-- 提现账户下拉 -->
<template>
  <div
    class="card-wrap"
    :style="{
      background: cardInfo.backgroundColor,
      boxShadow: `0 10px 15px 0 ${cardInfo.backgroundColor}4d`,
    }"
    @click="() => handleClickCard(item)"
  >
    <div class="left">
      <WithdrawTypeIcon :icon="cardInfo.icon" class="card-logo" />
      <div class="card-info">
        <div class="card-name">{{ cardInfo.name }}</div>
        <div class="card-num">{{ formatCardNumber(item?.account_no) }}</div>
      </div>
    </div>
    <div class="right" v-if="showSelectIcon">
      <ZIcon type="icon-qianjin" :size="18" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from "vue";
import { formatCardNumber } from "@/utils/core/tools";
import { getMethodsInfo } from "@/utils/config/GlobalConstant";
import { useWithdrawStore } from "@/stores/withdraw";
import WithdrawTypeIcon from "@/components/ZComonImg/WithdrawTypeIcon.vue";

const emits = defineEmits(["select"]);

const props = defineProps({
  // 参数透传
  item: {
    type: Object,
    required: true,
    default: () => ({}),
  },
  // 是否显示下拉
  showSelectIcon: {
    type: Boolean,
    required: false,
    default: false,
  },
});

const activeChannel = computed(() => {
  const withdrawStore = useWithdrawStore();
  const list = withdrawStore.withdrawData || [];
  const activeChannel = list.find((item) => item.account_type === props.item?.type);
  return activeChannel;
});

const cardInfo = computed(() => {
  const base = getMethodsInfo(props.item?.type);
  return {
    name: base.name,
    backgroundColor: activeChannel.value ? base.activeColor : base.unActiveColor,
    icon: activeChannel.value?.icon,
  };
});

const handleClickCard = (item) => {
  emits("select", item);
};
</script>

<style scoped lang="scss">
.card-wrap {
  width: 100%;
  height: 68px;
  border-radius: 20px;
  color: #fff;
  padding: 16px 20px;
  box-sizing: border-box;
  /* 可根据需求添加阴影等样式 */
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  box-shadow: 0 10px 15px 0 rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;

  .left {
    display: flex;
    align-items: center;
    justify-content: center;

    .card-logo {
      margin-right: 10px;
    }

    .card-info {
      .card-name {
        color: #fff;
        font-size: 18px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        margin-bottom: 2px;
      }

      .card-num {
        color: #fff;
        font-family: "D-DIN";
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
      }
    }
  }
  .right {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}
</style>
