export enum Status {
  EDIT = "edit",
  CHECK = "check",
  VIEW = "view",
}

export interface Item {
  account_id: string;
  account_no: string;
  type: number;
  [key: string]: any;
}

// 常量
export const CARD_THEMES = {
  gcash: {
    boxShadow: "0 4px 8px 0 #39B0FF66",
    background: "linear-gradient(90deg, #39B0FF 0%, #4881ED 100%)",
    checkBoxColor: "#004CDD",
    name: "Gcash",
    textColor: "#fff",
  },
  maya: {
    boxShadow: "0 4px 8px 0 #4BDE5E66",
    background: "linear-gradient(90deg, #4BDE5E 0%, #00C763 100%)",
    checkBoxColor: "#00A74C",
    name: "<PERSON>",
    textColor: "#fff",
  },
  bank: {
    boxShadow: "0 4px 8px 0 #FFB70066",
    background: "linear-gradient(90deg, #FFB700 0%, #FF9500 100%)",
    checkBoxColor: "#E16500",
    name: "Default",
    textColor: "#fff",
  },
  palawan: {
    boxShadow: "0 4px 8px 0 #00D3AD66",
    background: "linear-gradient(90deg, #00D3AD 0%, #00BB95 100%)",
    checkBoxColor: "#009072",
    name: "Default",
    textColor: "#fff",
  },
  QRPH: {
    boxShadow: "0 4px 8px 0 #FF424366",
    background: "linear-gradient(90deg, #FF7878 0%, #FF4243 100%)",
    checkBoxColor: "#D20A0A",
    name: "Default",
    textColor: "#fff",
  },
  default: {
    boxShadow: "0 4px 8px 0 #01d46a66",
    background: "#01d46a",
    checkBoxColor: "#00A74C",
    name: "Default",
    textColor: "#fff",
  },
} as const;
